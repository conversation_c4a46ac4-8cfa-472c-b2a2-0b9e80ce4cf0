'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  CheckCircleIcon, 
  XMarkIcon,
  CurrencyEuroIcon,
  ClockIcon,
  ShieldCheckIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const pricingTiers = [
  {
    name: 'Ba<PERSON>',
    description: 'Voor kleine klussen en reparaties',
    price: '65',
    period: 'per uur',
    features: [
      'Kleine reparaties',
      'Montage werk',
      'Basis elektra',
      'Eenvoudig loodgieterswerk',
      'Garantie op werk',
      'Geen voorrijkosten'
    ],
    notIncluded: [
      'Materiaalkosten',
      'Spoedtoeslag',
      'Weekend/avondwerk'
    ],
    popular: false,
    color: 'blue'
  },
  {
    name: 'Standaard',
    description: 'Voor reguliere klussen en projecten',
    price: '75',
    period: 'per uur',
    features: [
      'Alle basis diensten',
      'Complexere elektra',
      'Loodgieterswerk',
      'Schilderwerk',
      'Tegelwerk',
      '<PERSON><PERSON><PERSON> offerte',
      'Materiaal advies',
      '2 jaar garantie'
    ],
    notIncluded: [
      'Mat<PERSON>alkosten',
      'Spoedtoeslag'
    ],
    popular: true,
    color: 'green'
  },
  {
    name: 'Premium',
    description: 'Voor grote projecten en renovaties',
    price: '85',
    period: 'per uur',
    features: [
      'Alle standaard diensten',
      'Projectmanagement',
      'Maatwerk oplossingen',
      'Prioriteit planning',
      'Uitgebreide garantie',
      'Materiaal inkoop service',
      'Weekend beschikbaarheid',
      '5 jaar garantie'
    ],
    notIncluded: [
      'Materiaalkosten (wel korting)'
    ],
    popular: false,
    color: 'purple'
  }
];

const additionalCosts = [
  {
    item: 'Voorrijkosten',
    price: 'Gratis',
    description: 'Binnen ons werkgebied'
  },
  {
    item: 'Spoedtoeslag',
    price: '€25',
    description: 'Voor klussen binnen 4 uur'
  },
  {
    item: 'Avond/weekend',
    price: '+25%',
    description: 'Na 18:00 en in weekenden'
  },
  {
    item: 'Materialen',
    price: 'Kostprijs + 15%',
    description: 'Wij regelen de inkoop voor u'
  }
];

const faqs = [
  {
    question: 'Zijn er verborgen kosten?',
    answer: 'Nee, wij werken volledig transparant. U krijgt vooraf een duidelijke offerte met alle kosten.'
  },
  {
    question: 'Wanneer moet ik betalen?',
    answer: 'Betaling is mogelijk na oplevering van de klus. Wij accepteren contant, pin en bankoverschrijving.'
  },
  {
    question: 'Krijg ik garantie op het werk?',
    answer: 'Ja, wij geven minimaal 1 jaar garantie op al ons werk. Voor Premium klanten zelfs 5 jaar.'
  },
  {
    question: 'Zijn de materialen inbegrepen?',
    answer: 'Materialen worden apart berekend tegen kostprijs + 15% voor inkoop en transport.'
  }
];

export default function TarievenPage() {
  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Transparante <span className="text-blue-300">Tarieven</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Eerlijke prijzen zonder verrassingen. U weet vooraf waar u aan toe bent.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Pricing Tiers */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Kies het juiste tarief voor uw klus
            </h2>
            <p className="text-xl text-gray-600">
              Van kleine reparaties tot grote projecten
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingTiers.map((tier, index) => (
              <motion.div
                key={tier.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 ${
                  tier.popular ? 'ring-2 ring-green-500' : ''
                }`}
              >
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-green-500 text-white px-6 py-2 rounded-full text-sm font-semibold">
                      Meest Gekozen
                    </span>
                  </div>
                )}

                <div className="p-8">
                  {/* Header */}
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                    <p className="text-gray-600 mb-6">{tier.description}</p>
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold text-gray-900">€{tier.price}</span>
                      <span className="text-gray-500 ml-2">{tier.period}</span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    <h4 className="font-semibold text-gray-900">Inbegrepen:</h4>
                    {tier.features.map((feature) => (
                      <div key={feature} className="flex items-center space-x-3">
                        <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-600">{feature}</span>
                      </div>
                    ))}
                    
                    {tier.notIncluded.length > 0 && (
                      <>
                        <h4 className="font-semibold text-gray-900 mt-6">Niet inbegrepen:</h4>
                        {tier.notIncluded.map((item) => (
                          <div key={item} className="flex items-center space-x-3">
                            <XMarkIcon className="w-5 h-5 text-red-400 flex-shrink-0" />
                            <span className="text-gray-500">{item}</span>
                          </div>
                        ))}
                      </>
                    )}
                  </div>

                  {/* CTA */}
                  <Link
                    href="/klus-plaatsen"
                    className={`block w-full text-center px-6 py-3 rounded-lg font-semibold transition-colors ${
                      tier.popular
                        ? 'bg-green-600 text-white hover:bg-green-700'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    Klus plaatsen
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Costs */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Aanvullende Kosten
            </h2>
            <p className="text-xl text-gray-600">
              Overzicht van eventuele extra kosten
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {additionalCosts.map((cost, index) => (
              <motion.div
                key={cost.item}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-xl p-6 text-center"
              >
                <CurrencyEuroIcon className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{cost.item}</h3>
                <p className="text-2xl font-bold text-blue-600 mb-2">{cost.price}</p>
                <p className="text-sm text-gray-600">{cost.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <ShieldCheckIcon className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">Geen Verrassingen</h3>
              <p className="text-gray-600">
                Alle kosten worden vooraf duidelijk gecommuniceerd
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <ClockIcon className="w-16 h-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">Flexibele Betaling</h3>
              <p className="text-gray-600">
                Betaal na oplevering, contant, pin of overschrijving
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <StarIcon className="w-16 h-16 text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">Garantie Inbegrepen</h3>
              <p className="text-gray-600">
                Minimaal 1 jaar garantie op al ons werk
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Veelgestelde Vragen
            </h2>
            <p className="text-xl text-gray-600">
              Antwoorden op de meest gestelde vragen over onze tarieven
            </p>
          </motion.div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={faq.question}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-xl p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {faq.answer}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Klaar voor een vrijblijvende offerte?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Vraag vandaag nog een offerte aan en ontdek wat wij voor u kunnen betekenen
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/klus-plaatsen"
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-colors"
              >
                Gratis offerte aanvragen
              </Link>
              <a
                href="tel:+31612345678"
                className="bg-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-900 transition-colors"
              >
                Bel voor advies: 06-12345678
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
