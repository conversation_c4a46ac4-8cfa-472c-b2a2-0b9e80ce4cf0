'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  WrenchScrewdriverIcon, 
  BoltIcon, 
  PaintBrushIcon, 
  CubeIcon,
  HomeIcon,
  CogIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

const services = [
  {
    id: 'loodgieterswerk',
    icon: WrenchScrewdriverIcon,
    title: 'Loodgieterswerk',
    description: 'Professionele loodgietersservice voor al uw sanitaire klussen',
    features: [
      'Lekkage reparatie',
      'Kraan installatie en reparatie',
      'Toilet en douche installatie',
      'Afvoer ontstopping',
      'CV-ketel onderhoud',
      'Radiator installatie'
    ],
    pricing: 'Vanaf €65 per uur',
    urgentAvailable: true
  },
  {
    id: 'elektra',
    icon: BoltIcon,
    title: 'Elektra',
    description: 'Veilige elektrische installaties door gecertificeerde elektriciens',
    features: [
      'Stopcontacten installeren',
      'Verlichting aansluiten',
      'Schakelaars vervangen',
      'Meterkast uitbreiden',
      'Aardlekschakelaar installeren',
      'Elektrische keuring'
    ],
    pricing: 'Vanaf €70 per uur',
    urgentAvailable: true
  },
  {
    id: 'schilderwerk',
    icon: PaintBrushIcon,
    title: 'Schilderwerk',
    description: 'Vakkundig binnen- en buitenschilderwerk voor een frisse uitstraling',
    features: [
      'Binnenschilderwerk',
      'Buitenschilderwerk',
      'Behang aanbrengen',
      'Houtrot behandeling',
      'Spuitwerk',
      'Decoratieve technieken'
    ],
    pricing: 'Vanaf €45 per uur',
    urgentAvailable: false
  },
  {
    id: 'montage',
    icon: CubeIcon,
    title: 'Montage',
    description: 'Professionele montage van meubels, keukens en inbouwapparatuur',
    features: [
      'IKEA meubelmontage',
      'Keuken installatie',
      'Kastenwand montage',
      'Inbouwapparatuur',
      'Badkamermeubels',
      'Kantoormeubels'
    ],
    pricing: 'Vanaf €55 per uur',
    urgentAvailable: false
  },
  {
    id: 'tegelwerk',
    icon: HomeIcon,
    title: 'Tegelwerk',
    description: 'Vakkundig tegelwerk voor badkamers, keukens en vloeren',
    features: [
      'Badkamertegels',
      'Keukentegels',
      'Vloertegels',
      'Wandtegels',
      'Voegen vernieuwen',
      'Tegelreparatie'
    ],
    pricing: 'Vanaf €60 per uur',
    urgentAvailable: false
  },
  {
    id: 'timmerwerk',
    icon: CogIcon,
    title: 'Timmerwerk',
    description: 'Maatwerk timmerwerk en reparaties in hout',
    features: [
      'Maatwerk kasten',
      'Deuren ophangen',
      'Kozijnen repareren',
      'Lambrisering',
      'Plafonds',
      'Houten vloeren'
    ],
    pricing: 'Vanaf €65 per uur',
    urgentAvailable: false
  }
];

export default function DienstenPage() {
  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Onze <span className="text-blue-300">Diensten</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Van kleine reparaties tot grote projecten - wij hebben de expertise 
              voor al uw klussen in en om het huis.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
              >
                {/* Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                      <service.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">{service.title}</h3>
                      <p className="text-blue-600 font-semibold">{service.pricing}</p>
                    </div>
                  </div>
                  {service.urgentAvailable && (
                    <span className="bg-red-100 text-red-800 text-xs font-semibold px-3 py-1 rounded-full">
                      Spoed mogelijk
                    </span>
                  )}
                </div>

                {/* Description */}
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <div className="mb-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Wat wij doen:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {service.features.map((feature) => (
                      <div key={feature} className="flex items-center space-x-2">
                        <CheckCircleIcon className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-gray-600 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA */}
                <div className="flex space-x-4">
                  <Link
                    href="/klus-plaatsen"
                    className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold text-center hover:bg-blue-700 transition-colors"
                  >
                    Klus plaatsen
                  </Link>
                  <Link
                    href="/contact"
                    className="flex items-center space-x-2 text-blue-600 font-semibold hover:text-blue-700 transition-colors"
                  >
                    <span>Meer info</span>
                    <ArrowRightIcon className="w-4 h-4" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Waarom kiezen voor ASklussen.nl?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Onze vakmannen staan garant voor kwaliteit en betrouwbaarheid
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Gecertificeerd</h3>
              <p className="text-gray-600">
                Al onze vakmannen zijn volledig gecertificeerd en verzekerd
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircleIcon className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Garantie</h3>
              <p className="text-gray-600">
                Wij geven garantie op al ons werk en gebruikte materialen
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircleIcon className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Transparant</h3>
              <p className="text-gray-600">
                Eerlijke prijzen zonder verrassingen, u weet vooraf waar u aan toe bent
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Klaar om uw klus te laten uitvoeren?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Vraag vandaag nog een vrijblijvende offerte aan
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/klus-plaatsen"
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-colors"
              >
                Plaats uw klus
              </Link>
              <a
                href="tel:+31612345678"
                className="bg-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-900 transition-colors"
              >
                Bel direct: 06-12345678
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
