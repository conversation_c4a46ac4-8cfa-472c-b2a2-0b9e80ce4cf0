'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

const categories = [
  'Alle projecten',
  'Loodgieterswerk',
  'Elektra',
  'Schilderwerk',
  'Montage',
  'Tegelwerk',
  'Timmerwerk',
  'Renovatie'
];

const projects = [
  {
    id: 1,
    title: 'Badkamer renovatie Amsterdam',
    category: 'Renovatie',
    description: 'Complete badkamerrenovatie inclusief tegelwerk, sanitair en verlichting.',
    duration: '5 dagen',
    location: 'Amsterdam Noord',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Tegelwerk', 'Loodgieterswerk', 'Elektra']
  },
  {
    id: 2,
    title: 'Keuken installatie Utrecht',
    category: 'Montage',
    description: 'IKEA keuken montage met inbouwapparatuur en werkblad installatie.',
    duration: '2 dagen',
    location: 'Utrecht Centrum',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Mont<PERSON>', 'Elektra']
  },
  {
    id: 3,
    title: 'Woonkamer schilderwerk',
    category: 'Schilderwerk',
    description: 'Complete woonkamer geschilderd inclusief plafond en houtwerk.',
    duration: '3 dagen',
    location: 'Amsterdam West',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Schilderwerk']
  },
  {
    id: 4,
    title: 'CV-ketel vervanging',
    category: 'Loodgieterswerk',
    description: 'Oude CV-ketel vervangen door moderne HR-ketel inclusief leidingwerk.',
    duration: '1 dag',
    location: 'Amstelveen',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Loodgieterswerk']
  },
  {
    id: 5,
    title: 'Meterkast uitbreiding',
    category: 'Elektra',
    description: 'Meterkast uitgebreid met extra groepen en aardlekschakelaars.',
    duration: '1 dag',
    location: 'Haarlem',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Elektra']
  },
  {
    id: 6,
    title: 'Toilet renovatie',
    category: 'Tegelwerk',
    description: 'Toilet volledig opnieuw betegeld met moderne wandtegels.',
    duration: '2 dagen',
    location: 'Amsterdam Zuid',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Tegelwerk', 'Loodgieterswerk']
  },
  {
    id: 7,
    title: 'Maatwerk boekenkast',
    category: 'Timmerwerk',
    description: 'Op maat gemaakte boekenkast van vloer tot plafond.',
    duration: '4 dagen',
    location: 'Zaandam',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Timmerwerk']
  },
  {
    id: 8,
    title: 'Kantoor verlichting',
    category: 'Elektra',
    description: 'LED verlichting geïnstalleerd in kantoorruimte met dimfunctie.',
    duration: '1 dag',
    location: 'Amsterdam Oost',
    beforeImage: '/api/placeholder/400/300',
    afterImage: '/api/placeholder/400/300',
    tags: ['Elektra']
  }
];

export default function ProjectenPage() {
  const [selectedCategory, setSelectedCategory] = useState('Alle projecten');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<typeof projects[0] | null>(null);

  const filteredProjects = projects.filter(project => {
    const matchesCategory = selectedCategory === 'Alle projecten' || project.category === selectedCategory;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Onze <span className="text-blue-300">Projecten</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Bekijk een selectie van onze afgeronde projecten en laat u inspireren
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filters */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Zoek projecten..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {filteredProjects.length} project{filteredProjects.length !== 1 ? 'en' : ''} gevonden
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden group"
              >
                {/* Project Image */}
                <div className="relative h-48 bg-gray-200 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-gray-400 text-center">
                      <div className="w-16 h-16 bg-gray-300 rounded-lg mx-auto mb-2"></div>
                      <p className="text-sm">Voor/na foto's</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedProject(project)}
                    className="absolute top-4 right-4 z-20 bg-white/90 backdrop-blur-sm p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <EyeIcon className="w-5 h-5 text-gray-700" />
                  </button>
                </div>

                {/* Project Info */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {project.title}
                    </h3>
                    <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full">
                      {project.category}
                    </span>
                  </div>

                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {project.description}
                  </p>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Duur:</span>
                      <span className="font-medium text-gray-900">{project.duration}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Locatie:</span>
                      <span className="font-medium text-gray-900">{project.location}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2">
                    {project.tags.map((tag) => (
                      <span
                        key={tag}
                        className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <MagnifyingGlassIcon className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Geen projecten gevonden
              </h3>
              <p className="text-gray-600">
                Probeer een andere zoekterm of categorie
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Project Modal */}
      {selectedProject && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {selectedProject.title}
                  </h2>
                  <span className="bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full">
                    {selectedProject.category}
                  </span>
                </div>
                <button
                  onClick={() => setSelectedProject(null)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Voor</h3>
                  <div className="h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400">Voor foto</span>
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Na</h3>
                  <div className="h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400">Na foto</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <p className="text-gray-600 leading-relaxed">
                  {selectedProject.description}
                </p>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-gray-500">Duur:</span>
                    <span className="ml-2 font-medium">{selectedProject.duration}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Locatie:</span>
                    <span className="ml-2 font-medium">{selectedProject.location}</span>
                  </div>
                </div>

                <div>
                  <span className="text-gray-500">Diensten:</span>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {selectedProject.tags.map((tag) => (
                      <span
                        key={tag}
                        className="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Wilt u ook zo'n mooi resultaat?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Laat ons uw project realiseren met dezelfde zorg en vakmanschap
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/klus-plaatsen"
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-colors"
              >
                Start uw project
              </a>
              <a
                href="/contact"
                className="bg-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-900 transition-colors"
              >
                Vraag advies
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
