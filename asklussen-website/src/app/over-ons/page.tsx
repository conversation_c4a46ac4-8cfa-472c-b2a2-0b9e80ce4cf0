'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  UserGroupIcon, 
  ShieldCheckIcon, 
  StarIcon, 
  ClockIcon,
  HeartIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';

const values = [
  {
    icon: ShieldCheckIcon,
    title: '<PERSON>rou<PERSON>baarheid',
    description: 'Wij staan voor onze afspraken en leveren altijd wat wij beloven. Uw vertrouwen is ons belangrijkste bezit.'
  },
  {
    icon: StarIcon,
    title: 'Kwaliteit',
    description: 'Vakmanschap staat bij ons voorop. Wij werken alleen met de beste materialen en technieken.'
  },
  {
    icon: HeartIcon,
    title: 'Klanttevredenheid',
    description: '<PERSON>w tevredenheid is ons doel. Wij gaan pas naar huis als u 100% tevreden bent met het resultaat.'
  },
  {
    icon: ClockIcon,
    title: 'Snelheid',
    description: 'Wij begrijpen dat tijd kostbaar is. Daarom werken wij efficiënt en houden wij ons aan afspraken.'
  }
];

const team = [
  {
    name: '<PERSON>',
    role: 'Oprichter & Hoofdvakman',
    specialization: '<PERSON>odgieterswerk & Elektra',
    experience: '15+ jaar ervaring',
    description: 'Alex startte ASklussen.nl vanuit zijn passie voor vakmanschap en klantenservice.'
  },
  {
    name: 'Marco van der Berg',
    role: 'Senior Vakman',
    specialization: 'Schilderwerk & Timmerwerk',
    experience: '12+ jaar ervaring',
    description: 'Marco is onze specialist voor alle schilder- en timmerklussen, van klein tot groot.'
  },
  {
    name: 'Stefan Jansen',
    role: 'Montage Specialist',
    specialization: 'Montage & Tegelwerk',
    experience: '10+ jaar ervaring',
    description: 'Stefan zorgt ervoor dat alles perfect op zijn plaats komt, van keukens tot badkamers.'
  }
];

const milestones = [
  {
    year: '2018',
    title: 'Start ASklussen.nl',
    description: 'Alex Smit start zijn eigen klusbedrijf met de missie om betrouwbare service te leveren.'
  },
  {
    year: '2019',
    title: 'Eerste 100 klanten',
    description: 'Door mond-tot-mond reclame groeit het klantenbestand snel naar 100 tevreden klanten.'
  },
  {
    year: '2020',
    title: 'Team uitbreiding',
    description: 'Marco en Stefan sluiten zich aan bij het team om de groeiende vraag te kunnen bedienen.'
  },
  {
    year: '2021',
    title: '500+ projecten',
    description: 'ASklussen.nl heeft inmiddels meer dan 500 succesvolle projecten afgerond.'
  },
  {
    year: '2023',
    title: 'Digitale innovatie',
    description: 'Lancering van de nieuwe website met online klus plaatsen en afspraak maken.'
  },
  {
    year: '2024',
    title: 'Uitbreiding werkgebied',
    description: 'Uitbreiding van het werkgebied naar heel Amsterdam en omliggende gemeenten.'
  }
];

export default function OverOnsPage() {
  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Over <span className="text-blue-300">ASklussen.nl</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Uw betrouwbare partner voor alle klussen in en om het huis sinds 2018
            </p>
          </motion.div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                Ons Verhaal
              </h2>
              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <p>
                  ASklussen.nl werd in 2018 opgericht door Alex Smit, een ervaren vakman met een 
                  passie voor kwaliteit en klantenservice. Gefrustreerd door de slechte ervaringen 
                  die veel mensen hadden met klusjesmannen, besloot Alex zijn eigen bedrijf te starten.
                </p>
                <p>
                  Onze missie is eenvoudig: betrouwbare, professionele klusservice leveren tegen 
                  een eerlijke prijs. Wij geloven dat elke klant recht heeft op vakmanschap van 
                  hoge kwaliteit, ongeacht de grootte van de klus.
                </p>
                <p>
                  Vandaag de dag zijn wij uitgegroeid tot een team van ervaren vakmannen die 
                  dagelijks klanten helpen met hun klussen. Van kleine reparaties tot grote 
                  renovaties - wij doen het allemaal met dezelfde toewijding en zorg.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <div className="grid grid-cols-2 gap-6 text-center">
                <div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
                  <div className="text-gray-600">Tevreden Klanten</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-green-600 mb-2">6+</div>
                  <div className="text-gray-600">Jaar Ervaring</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-purple-600 mb-2">98%</div>
                  <div className="text-gray-600">Klanttevredenheid</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-orange-600 mb-2">24/7</div>
                  <div className="text-gray-600">Bereikbaar</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Onze Waarden
            </h2>
            <p className="text-xl text-gray-600">
              Deze principes staan centraal in alles wat wij doen
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <value.icon className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-600 leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Ons Team
            </h2>
            <p className="text-xl text-gray-600">
              Maak kennis met de vakmannen achter ASklussen.nl
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="text-center mb-6">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <UserGroupIcon className="w-12 h-12 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                  <p className="text-blue-600 font-semibold mb-2">{member.role}</p>
                  <p className="text-gray-500 text-sm">{member.experience}</p>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Specialisatie:</h4>
                    <p className="text-gray-600">{member.specialization}</p>
                  </div>
                  <p className="text-gray-600 leading-relaxed">{member.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Onze Geschiedenis
            </h2>
            <p className="text-xl text-gray-600">
              Van startup tot gevestigde naam in de klusbranche
            </p>
          </motion.div>

          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.year}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
              >
                <div className={`flex-1 ${index % 2 === 0 ? 'text-right pr-8' : 'text-left pl-8'}`}>
                  <div className="bg-gray-50 rounded-xl p-6">
                    <div className="text-blue-600 font-bold text-lg mb-2">{milestone.year}</div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{milestone.title}</h3>
                    <p className="text-gray-600">{milestone.description}</p>
                  </div>
                </div>
                
                <div className="w-4 h-4 bg-blue-600 rounded-full flex-shrink-0 relative">
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-white rounded-full"></div>
                </div>
                
                <div className="flex-1"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-blue-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Klaar om met ons samen te werken?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Ervaar zelf waarom honderden klanten ons vertrouwen
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/klus-plaatsen"
                className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-colors"
              >
                Plaats uw klus
              </Link>
              <Link
                href="/contact"
                className="bg-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-900 transition-colors"
              >
                Neem contact op
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
