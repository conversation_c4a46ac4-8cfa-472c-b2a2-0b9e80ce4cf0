import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "ASklussen.nl - Professionele Klusservice",
  description: "Betrouwbare en professionele klusservice voor al uw klussen. Van loodgieterswerk tot elektra, van schilderwerk tot montage. Vraag direct een offerte aan!",
  keywords: "klusservice, handyman, loodgieter, elektricien, schilder, montage, klussen, reparatie",
  authors: [{ name: "ASklussen.nl" }],
  openGraph: {
    title: "ASklussen.nl - Professionele Klusservice",
    description: "Betrouwbare en professionele klusservice voor al uw klussen.",
    type: "website",
    locale: "nl_NL",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="nl" className="scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased`}>
        <Navigation />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
