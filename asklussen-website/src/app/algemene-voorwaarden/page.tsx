'use client';

import { motion } from 'framer-motion';

const sections = [
  {
    title: 'Artikel 1: Definities',
    content: [
      'ASklussen.nl: de onderneming ASklussen.nl, gevestigd te Amsterdam.',
      'Klant: de natuurlijke of rechtspersoon die een overeenkomst aangaat met ASklussen.nl.',
      'Diensten: alle werkzaamheden die door ASklussen.nl worden uitgevoerd.',
      'Offerte: een voorstel van ASklussen.nl aan de klant voor het uitvoeren van bepaalde diensten.'
    ]
  },
  {
    title: 'Artikel 2: Toepasselijkheid',
    content: [
      'Deze algemene voorwaarden zijn van toepassing op alle aanbiedingen, offertes en overeenkomsten tussen ASklussen.nl en de klant.',
      'Afwijkingen van deze voorwaarden zijn alleen geldig indien deze schriftelijk zijn overeengekomen.',
      'Eventuele inkoop- of andere voorwaarden van de klant zijn niet van toepassing.'
    ]
  },
  {
    title: 'Artikel 3: Offertes en Prijzen',
    content: [
      'Alle offertes zijn vrijblijvend en geldig gedurende 30 dagen, tenzij anders vermeld.',
      'Prijzen zijn exclusief BTW en eventuele andere heffingen, tenzij anders vermeld.',
      'ASklussen.nl behoudt zich het recht voor om prijzen aan te passen bij wijziging van kostprijsbepalende factoren.',
      'Materiaalkosten worden apart berekend tegen kostprijs plus 15% voor inkoop en transport.'
    ]
  },
  {
    title: 'Artikel 4: Totstandkoming Overeenkomst',
    content: [
      'Een overeenkomst komt tot stand door schriftelijke bevestiging van de klant of door aanvang van de werkzaamheden.',
      'Mondelinge afspraken zijn alleen bindend na schriftelijke bevestiging.',
      'ASklussen.nl behoudt zich het recht voor om opdrachten te weigeren zonder opgaaf van redenen.'
    ]
  },
  {
    title: 'Artikel 5: Uitvoering van de Diensten',
    content: [
      'ASklussen.nl voert de diensten uit conform de gemaakte afspraken en volgens de regels van vakmanschap.',
      'De klant dient te zorgen voor toegang tot de werklocatie en de benodigde faciliteiten.',
      'Wijzigingen in de opdracht kunnen leiden tot aanpassing van prijs en planning.',
      'ASklussen.nl is gerechtigd werkzaamheden uit te stellen bij onvoorziene omstandigheden.'
    ]
  },
  {
    title: 'Artikel 6: Betaling',
    content: [
      'Betaling dient te geschieden binnen 14 dagen na oplevering van de werkzaamheden.',
      'Bij overschrijding van de betalingstermijn is de klant van rechtswege in verzuim.',
      'Over het openstaande bedrag is rente verschuldigd van 1% per maand.',
      'Alle kosten van invordering komen voor rekening van de klant.'
    ]
  },
  {
    title: 'Artikel 7: Garantie',
    content: [
      'ASklussen.nl geeft 12 maanden garantie op uitgevoerde werkzaamheden.',
      'De garantie geldt niet voor normale slijtage, verkeerd gebruik of externe beschadiging.',
      'Garantie vervalt bij reparaties door derden zonder toestemming van ASklussen.nl.',
      'Materialen vallen onder de garantievoorwaarden van de leverancier.'
    ]
  },
  {
    title: 'Artikel 8: Aansprakelijkheid',
    content: [
      'ASklussen.nl is verzekerd voor beroepsaansprakelijkheid en bedrijfsschade.',
      'Aansprakelijkheid is beperkt tot het bedrag dat door de verzekering wordt uitgekeerd.',
      'ASklussen.nl is niet aansprakelijk voor indirecte schade of gevolgschade.',
      'De klant dient schade binnen 24 uur schriftelijk te melden.'
    ]
  },
  {
    title: 'Artikel 9: Overmacht',
    content: [
      'Bij overmacht is ASklussen.nl gerechtigd de uitvoering op te schorten of de overeenkomst te ontbinden.',
      'Onder overmacht wordt verstaan: ziekte, weersomstandigheden, stakingen, en andere onvoorziene omstandigheden.',
      'In geval van overmacht heeft de klant geen recht op schadevergoeding.'
    ]
  },
  {
    title: 'Artikel 10: Geschillen',
    content: [
      'Op alle overeenkomsten is Nederlands recht van toepassing.',
      'Geschillen worden voorgelegd aan de bevoegde rechter in Amsterdam.',
      'Partijen zullen eerst trachten geschillen in onderling overleg op te lossen.'
    ]
  }
];

export default function AlgemeneVoorwaardenPage() {
  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 to-blue-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Algemene <span className="text-blue-300">Voorwaarden</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Transparante voorwaarden voor een duidelijke samenwerking
            </p>
          </motion.div>
        </div>
      </section>

      {/* Content */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-white rounded-2xl p-8 shadow-lg mb-8"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Algemene Voorwaarden ASklussen.nl
              </h2>
              <p className="text-gray-600">
                Laatst bijgewerkt: 1 januari 2024
              </p>
            </div>

            <div className="prose prose-lg max-w-none">
              <p className="text-gray-600 leading-relaxed mb-8">
                Deze algemene voorwaarden zijn van toepassing op alle diensten die worden 
                geleverd door ASklussen.nl. Door gebruik te maken van onze diensten gaat u 
                akkoord met deze voorwaarden.
              </p>
            </div>
          </motion.div>

          <div className="space-y-8">
            {sections.map((section, index) => (
              <motion.div
                key={section.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-8 shadow-lg"
              >
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  {section.title}
                </h3>
                <ul className="space-y-4">
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-gray-600 leading-relaxed">{item}</p>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-blue-50 rounded-2xl p-8 mt-12"
          >
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Vragen over deze voorwaarden?
            </h3>
            <p className="text-gray-600 mb-6">
              Heeft u vragen over deze algemene voorwaarden? Neem dan contact met ons op. 
              Wij helpen u graag verder.
            </p>
            <div className="space-y-2 text-gray-700">
              <p><strong>ASklussen.nl</strong></p>
              <p>Voorbeeldstraat 123</p>
              <p>1234 AB Amsterdam</p>
              <p>Telefoon: 06-12345678</p>
              <p>E-mail: <EMAIL></p>
              <p>KvK nummer: 12345678</p>
              <p>BTW nummer: NL123456789B01</p>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
