'use client';

import { motion } from 'framer-motion';
import { StarIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';

const testimonials = [
  {
    name: '<PERSON>',
    location: 'Amsterdam',
    rating: 5,
    text: 'Fantastische service! De loodgieter kwam binnen 2 uur en heeft mijn lekkende kraan perfect gerepareerd. Zeer professioneel en vriendelijk.',
    service: 'Loodgieterswerk',
    date: '2 weken geleden'
  },
  {
    name: '<PERSON>',
    location: 'Utrecht',
    rating: 5,
    text: 'Mijn keuken is prachtig betegeld door het team van ASklussen. Vakwerk van hoge kwaliteit en netjes opgeruimd achtergelaten.',
    service: 'Tegelwerk',
    date: '1 maand geleden'
  },
  {
    name: '<PERSON>',
    location: 'Rotterdam',
    rating: 5,
    text: 'Snelle en betrouwbare service voor het ophangen van lampen en het installeren van extra stopcontacten. Aanrader!',
    service: 'Elektra',
    date: '3 weken geleden'
  },
  {
    name: '<PERSON>',
    location: '<PERSON>',
    rating: 5,
    text: '<PERSON><PERSON> woonkamer geschilderd. Perfecte afwerking en zeer nette werkwijze. Prijs-kwaliteit verhouding is uitstekend.',
    service: 'Schilderwerk',
    date: '2 maanden geleden'
  },
  {
    name: 'Linda Bakker',
    location: 'Eindhoven',
    rating: 5,
    text: 'IKEA keuken gemonteerd binnen één dag. Zeer ervaren monteurs die weten wat ze doen. Top service!',
    service: 'Montage',
    date: '1 maand geleden'
  },
  {
    name: 'Tom van Dijk',
    location: 'Groningen',
    rating: 5,
    text: 'Badkamer volledig gerenoveerd. Van tegels tot sanitair, alles perfect uitgevoerd. Zeer tevreden met het resultaat.',
    service: 'Renovatie',
    date: '6 weken geleden'
  }
];

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <div key={star}>
          {star <= rating ? (
            <StarIcon className="w-5 h-5 text-yellow-400" />
          ) : (
            <StarOutlineIcon className="w-5 h-5 text-gray-300" />
          )}
        </div>
      ))}
    </div>
  );
}

export function TestimonialsSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Wat onze <span className="text-blue-600">klanten</span> zeggen
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Lees de ervaringen van onze tevreden klanten. Hun vertrouwen 
            en positieve feedback motiveren ons elke dag opnieuw.
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={`${testimonial.name}-${index}`}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 h-full flex flex-col">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h4 className="font-bold text-gray-900 text-lg">{testimonial.name}</h4>
                    <p className="text-gray-500 text-sm">{testimonial.location}</p>
                  </div>
                  <StarRating rating={testimonial.rating} />
                </div>

                {/* Service badge */}
                <div className="mb-4">
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full">
                    {testimonial.service}
                  </span>
                </div>

                {/* Testimonial text */}
                <blockquote className="text-gray-600 leading-relaxed mb-6 flex-1">
                  "{testimonial.text}"
                </blockquote>

                {/* Date */}
                <div className="text-gray-400 text-sm">
                  {testimonial.date}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom section with overall rating */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-4 mb-4">
              <div className="flex space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <StarIcon key={star} className="w-8 h-8 text-yellow-400" />
                ))}
              </div>
              <span className="text-3xl font-bold text-gray-900">4.9</span>
            </div>
            <p className="text-gray-600 text-lg mb-4">
              Gemiddelde beoordeling op basis van <strong>247 reviews</strong>
            </p>
            <p className="text-gray-500">
              98% van onze klanten beveelt ons aan bij familie en vrienden
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
