'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  WrenchScrewdriverIcon, 
  BoltIcon, 
  PaintBrushIcon, 
  CubeIcon,
  HomeIcon,
  CogIcon
} from '@heroicons/react/24/outline';

const services = [
  {
    icon: WrenchScrewdriverIcon,
    title: 'Loodgieterswerk',
    description: 'Lek<PERSON> repareren, kranen vervangen, sanitair installeren en alle andere loodgietersklussen.',
    features: ['Lekkage reparatie', '<PERSON>raan installatie', 'Sanitair', 'Ontstopping']
  },
  {
    icon: BoltIcon,
    title: 'Elektra',
    description: 'Veilige elektrische installaties, stopcontacten, schakelaars en verlichting.',
    features: ['Stopcontacten', 'Verlichting', 'Schakelaars', 'Meterkast']
  },
  {
    icon: PaintBrushIcon,
    title: 'Schilderwerk',
    description: 'Professioneel binnen- en buitenschilderwerk voor een frisse uitstraling.',
    features: ['Binnenschilderen', '<PERSON><PERSON><PERSON>childeren', 'Behangen', 'Voorbehandeling']
  },
  {
    icon: CubeIcon,
    title: 'Mont<PERSON>',
    description: '<PERSON><PERSON><PERSON>, keukens, kasten en andere montagewerk vakkundig uitgevoerd.',
    features: ['Meubelmontage', 'Keukeninstallatie', 'Kastenwand', 'IKEA montage']
  },
  {
    icon: HomeIcon,
    title: 'Tegelwerk',
    description: 'Badkamers, keukens en vloeren betegelen met oog voor detail.',
    features: ['Badkamertegels', 'Keukentegels', 'Vloertegels', 'Voegen']
  },
  {
    icon: CogIcon,
    title: 'Timmerwerk',
    description: 'Maatwerk timmerwerk, reparaties en aanpassingen in hout.',
    features: ['Maatwerk', 'Reparaties', 'Deuren', 'Kozijnen']
  }
];

export function ServicesSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Onze <span className="text-blue-600">Diensten</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Van kleine reparaties tot grote projecten - wij hebben de expertise 
            voor al uw klussen in en om het huis.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                {/* Icon */}
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-gray-500">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>

                {/* CTA */}
                <Link
                  href="/diensten"
                  className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors group-hover:translate-x-1 transform duration-200"
                >
                  Meer informatie
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-lg text-gray-600 mb-8">
            Staat uw klus er niet bij? Geen probleem! Wij helpen u graag met alle soorten klussen.
          </p>
          <Link
            href="/klus-plaatsen"
            className="inline-flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            Plaats uw klus
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
