'use client';

import { motion } from 'framer-motion';
import { 
  ShieldCheckIcon, 
  ClockIcon, 
  CurrencyEuroIcon, 
  UserGroupIcon,
  StarIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

const reasons = [
  {
    icon: ShieldCheckIcon,
    title: 'Betrouwbaar & Verzekerd',
    description: 'Volledig verzekerd en gecertificeerd. Uw klus is in veilige handen met onze ervaren vakmannen.',
    color: 'from-green-500 to-emerald-600'
  },
  {
    icon: ClockIcon,
    title: 'Snelle Service',
    description: 'Vaak nog dezelfde dag beschikbaar. Wij begrijpen dat sommige klussen niet kunnen wachten.',
    color: 'from-blue-500 to-cyan-600'
  },
  {
    icon: CurrencyEuroIcon,
    title: 'Eerlijke Prijzen',
    description: 'Transparante prijzen zonder verrassingen. U weet vooraf waar u aan toe bent.',
    color: 'from-purple-500 to-indigo-600'
  },
  {
    icon: UserGroupIcon,
    title: '<PERSON><PERSON><PERSON> Team',
    description: 'Ons team bestaat uit ervaren vakmannen met jarenlange expertise in hun vakgebied.',
    color: 'from-orange-500 to-red-600'
  },
  {
    icon: StarIcon,
    title: 'Kwaliteitsgarantie',
    description: 'Wij staan achter ons werk. Niet tevreden? Dan maken wij het kosteloos in orde.',
    color: 'from-yellow-500 to-orange-600'
  },
  {
    icon: PhoneIcon,
    title: '24/7 Bereikbaar',
    description: 'Voor spoedklussen zijn wij altijd bereikbaar. Dag en nacht, weekend en feestdagen.',
    color: 'from-teal-500 to-green-600'
  }
];

export function WhyChooseUsSection() {
  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-50"></div>
      <div className="absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-l from-blue-100 to-transparent rounded-full blur-3xl opacity-30"></div>
      <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-gradient-to-r from-indigo-100 to-transparent rounded-full blur-3xl opacity-30"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Waarom <span className="text-blue-600">ASklussen.nl</span>?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Ontdek waarom honderden klanten ons vertrouwen voor hun klussen. 
            Kwaliteit, betrouwbaarheid en service staan bij ons voorop.
          </p>
        </motion.div>

        {/* Reasons Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reasons.map((reason, index) => (
            <motion.div
              key={reason.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100/50">
                {/* Icon */}
                <div className={`w-16 h-16 bg-gradient-to-br ${reason.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <reason.icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                  {reason.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {reason.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-3xl p-8 md:p-12 text-white"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Cijfers die voor zich spreken
            </h3>
            <p className="text-blue-100 text-lg">
              Onze resultaten in getallen
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold">500+</div>
              <div className="text-blue-200">Tevreden Klanten</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold">98%</div>
              <div className="text-blue-200">Klanttevredenheid</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold">5★</div>
              <div className="text-blue-200">Gemiddelde Beoordeling</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl md:text-5xl font-bold">24u</div>
              <div className="text-blue-200">Reactietijd</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
