'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRightIcon, PhoneIcon, CalendarIcon } from '@heroicons/react/24/outline';

export function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-indigo-400/10 rounded-full blur-3xl"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          {/* Main heading */}
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Klaar om te beginnen?
          </h2>
          
          <p className="text-xl md:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Laat ons uw klus vakkundig uitvoeren. Vraag vandaag nog een 
            vrijblijvende offerte aan of plan direct een afspraak in.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="/klus-plaatsen"
                className="group bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 shadow-2xl hover:shadow-blue-500/25 flex items-center space-x-3"
              >
                <span>Plaats uw klus</span>
                <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="/afspraak-maken"
                className="group bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold text-lg border border-white/20 hover:bg-white/20 transition-all duration-300 flex items-center space-x-3"
              >
                <CalendarIcon className="w-5 h-5" />
                <span>Plan afspraak</span>
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <a
                href="tel:+31612345678"
                className="group bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center space-x-3"
              >
                <PhoneIcon className="w-5 h-5" />
                <span>Bel direct</span>
              </a>
            </motion.div>
          </div>

          {/* Contact info cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
            >
              <PhoneIcon className="w-8 h-8 text-blue-300 mx-auto mb-4" />
              <h3 className="text-white font-semibold text-lg mb-2">Bel ons</h3>
              <p className="text-blue-100 mb-3">Direct contact met onze experts</p>
              <a href="tel:+31612345678" className="text-blue-300 font-semibold hover:text-white transition-colors">
                06-12345678
              </a>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
            >
              <CalendarIcon className="w-8 h-8 text-blue-300 mx-auto mb-4" />
              <h3 className="text-white font-semibold text-lg mb-2">Plan online</h3>
              <p className="text-blue-100 mb-3">Kies zelf uw gewenste tijd</p>
              <Link href="/afspraak-maken" className="text-blue-300 font-semibold hover:text-white transition-colors">
                Afspraak maken
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20"
            >
              <ArrowRightIcon className="w-8 h-8 text-blue-300 mx-auto mb-4" />
              <h3 className="text-white font-semibold text-lg mb-2">Klus plaatsen</h3>
              <p className="text-blue-100 mb-3">Beschrijf uw klus online</p>
              <Link href="/klus-plaatsen" className="text-blue-300 font-semibold hover:text-white transition-colors">
                Start hier
              </Link>
            </motion.div>
          </div>

          {/* Trust indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            viewport={{ once: true }}
            className="mt-16 flex flex-wrap justify-center items-center gap-8 text-blue-200"
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>Verzekerd & Gecertificeerd</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>Geen voorrijkosten</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>Gratis offerte</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>100% Garantie</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
