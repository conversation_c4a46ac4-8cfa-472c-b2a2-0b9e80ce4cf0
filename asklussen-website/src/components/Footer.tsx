import Link from 'next/link';
import { PhoneIcon, EnvelopeIcon, MapPinIcon } from '@heroicons/react/24/outline';

const services = [
  'Loodgieterswerk',
  'Elektra',
  'Schilderwerk',
  'Montage',
  'Tegelwerk',
  'Timmerwerk',
];

const quickLinks = [
  { name: 'Home', href: '/' },
  { name: '<PERSON><PERSON><PERSON>', href: '/diensten' },
  { name: 'Tarieve<PERSON>', href: '/tarieven' },
  { name: 'Contact', href: '/contact' },
  { name: '<PERSON><PERSON>laats<PERSON>', href: '/klus-plaatsen' },
  { name: 'A<PERSON>praak Maken', href: '/afspraak-maken' },
];

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">AS</span>
              </div>
              <span className="text-xl font-bold">klussen.nl</span>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              Uw betrouwbare partner voor alle klussen in en om het huis. 
              Professioneel, snel en tegen een eerlijke prijs.
            </p>
            <div className="flex space-x-4">
              {/* Social media icons can be added here */}
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Onze Diensten</h3>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service}>
                  <Link
                    href="/diensten"
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {service}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Snelle Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <PhoneIcon className="w-5 h-5 text-blue-400 flex-shrink-0" />
                <a
                  href="tel:+31612345678"
                  className="text-gray-300 hover:text-white transition-colors text-sm"
                >
                  06-12345678
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <EnvelopeIcon className="w-5 h-5 text-blue-400 flex-shrink-0" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-300 hover:text-white transition-colors text-sm"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-start space-x-3">
                <MapPinIcon className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                <div className="text-gray-300 text-sm">
                  <p>Voorbeeldstraat 123</p>
                  <p>1234 AB Amsterdam</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © {new Date().getFullYear()} ASklussen.nl. Alle rechten voorbehouden.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link
              href="/algemene-voorwaarden"
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              Algemene Voorwaarden
            </Link>
            <Link
              href="/privacy"
              className="text-gray-400 hover:text-white transition-colors text-sm"
            >
              Privacy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
