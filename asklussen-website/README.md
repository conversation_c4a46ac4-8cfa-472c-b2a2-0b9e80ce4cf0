# ASklussen.nl - Professionele Klusservice Website

Een moderne, professionele website voor ASklussen.nl gebouwd met Next.js 14, React, TypeScript en Tailwind CSS.

## 🚀 Features

### Moderne Design & UX
- **Glassmorphism effecten** - Semi-transparante elementen met backdrop-blur
- **3D Card effecten** - Subtiele schaduwen en hover animaties
- **Smooth transitions** - Vloeiende animaties met Framer Motion
- **Parallax scrolling** - Dynamische teksteffecten
- **Responsief design** - Optimaal op alle apparaten (desktop, tablet, mobiel)
- **4K/Retina optimalisatie** - Scherpe afbeeldingen en iconen

### Pagina's & Functionaliteiten
1. **Home** - Krachtige introductie met CTA naar klus plaatsen
2. **Diensten** - Overzicht van alle services met iconen en beschrijvingen
3. **Tarieven** - Transparante prijsstructuur met verschillende pakketten
4. **Beoordelingen** - Klantgetuigenissen en reviews met filtering
5. **Contact** - Contact<PERSON>ulier en bedrijfsgegevens
6. **Over Ons** - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, team en waarden
7. **Projecten** - Portfolio met voor/na foto's en filtering
8. **Algemene Voorwaarden** - Juridische informatie
9. **Klus Plaatsen** - Stapsgewijze interface voor klus aanmelden
10. **Afspraak Maken** - Online booking systeem met kalender

### Technische Features
- **Next.js 14** met App Router
- **TypeScript** voor type safety
- **Tailwind CSS** voor styling
- **Framer Motion** voor animaties
- **React Hook Form** met Zod validatie
- **Headless UI** voor toegankelijke componenten
- **Heroicons & Lucide React** voor iconen

## 🛠️ Installatie & Setup

### Vereisten
- Node.js 18+
- npm of yarn

### Stappen
1. **Start development server**
   ```bash
   npm run dev
   ```

2. **Open in browser**
   ```
   http://localhost:3000
   ```

## 📁 Project Structuur

```
src/
├── app/                          # Next.js App Router
│   ├── globals.css              # Globale styling
│   ├── layout.tsx               # Root layout
│   ├── page.tsx                 # Homepage
│   ├── diensten/                # Diensten pagina
│   ├── tarieven/                # Tarieven pagina
│   ├── beoordelingen/           # Beoordelingen pagina
│   ├── contact/                 # Contact pagina
│   ├── over-ons/                # Over Ons pagina
│   ├── projecten/               # Projecten/Portfolio pagina
│   ├── klus-plaatsen/           # Klus plaatsen formulier
│   ├── afspraak-maken/          # Afspraak maken systeem
│   └── algemene-voorwaarden/    # Algemene voorwaarden
├── components/                   # Herbruikbare componenten
│   ├── Navigation.tsx           # Hoofdnavigatie
│   ├── Footer.tsx               # Footer component
│   └── home/                    # Homepage componenten
└── lib/
    └── utils.ts                 # Utility functies
```

## 🎨 Design System

### Kleuren
- **Primary**: Blue (verschillende tinten)
- **Secondary**: Gray (verschillende tinten)
- **Accent**: Cyan, Green, Purple, Orange

### Typografie
- **Font**: Inter (Google Fonts)
- **Responsive**: Typography scale

## 📱 Responsive Design

- **Mobile First**: Ontworpen voor mobiele apparaten
- **Breakpoints**: sm, md, lg, xl, 2xl

## 🔧 Aanpassingen

### Content Aanpassen
1. **Contactgegevens**: Update in Footer en contact pagina
2. **Bedrijfsinfo**: Wijzig in Over Ons pagina
3. **Diensten**: Pas aan in Diensten pagina
4. **Tarieven**: Update in Tarieven pagina

## 🚀 Deployment

### Build Commands
```bash
npm run build    # Production build
npm run start    # Start production server
npm run lint     # Code linting
```

## 📧 Contact

Voor vragen over de website:
- **Email**: <EMAIL>
- **Telefoon**: 06-12345678

---

**Ontwikkeld met ❤️ voor ASklussen.nl**
