{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ArrowRightIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\n\nconst features = [\n  'Betrouwbaar & Professioneel',\n  'Snelle Service',\n  'Eerlijke Prijzen',\n  'Vakmanschap Gegarandeerd'\n];\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background with gradient overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900\">\n        <div className=\"absolute inset-0 bg-black/20\"></div>\n        {/* Animated background pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-white rounded-full blur-3xl animate-float\"></div>\n          <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-300 rounded-full blur-3xl animate-float\" style={{ animationDelay: '2s' }}></div>\n        </div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Main heading */}\n          <div className=\"space-y-4\">\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight\"\n            >\n              Uw Klus,{' '}\n              <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\">\n                Onze Expertise\n              </span>\n            </motion.h1>\n            \n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed\"\n            >\n              Van kleine reparaties tot grote projecten - wij zorgen ervoor dat het goed gebeurt. \n              Betrouwbaar, professioneel en altijd tegen een eerlijke prijs.\n            </motion.p>\n          </div>\n\n          {/* Features */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"flex flex-wrap justify-center gap-4 md:gap-6\"\n          >\n            {features.map((feature, index) => (\n              <div\n                key={feature}\n                className=\"flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20\"\n              >\n                <CheckCircleIcon className=\"w-5 h-5 text-green-400\" />\n                <span className=\"text-white font-medium\">{feature}</span>\n              </div>\n            ))}\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center pt-8\"\n          >\n            <Link\n              href=\"/klus-plaatsen\"\n              className=\"group bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 flex items-center space-x-2\"\n            >\n              <span>Wat is uw klus?</span>\n              <ArrowRightIcon className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n            </Link>\n            \n            <Link\n              href=\"/contact\"\n              className=\"group bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold text-lg border border-white/20 hover:bg-white/20 transition-all duration-300 flex items-center space-x-2\"\n            >\n              <span>Bel Direct</span>\n              <span className=\"text-blue-300\">06-12345678</span>\n            </Link>\n          </motion.div>\n\n          {/* Trust indicators */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.0 }}\n            className=\"pt-12 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n          >\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl font-bold text-white\">500+</div>\n              <div className=\"text-blue-200 text-sm\">Tevreden Klanten</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl font-bold text-white\">24/7</div>\n              <div className=\"text-blue-200 text-sm\">Bereikbaar</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl font-bold text-white\">5★</div>\n              <div className=\"text-blue-200 text-sm\">Gemiddelde Beoordeling</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-3xl font-bold text-white\">100%</div>\n              <div className=\"text-blue-200 text-sm\">Garantie</div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white/50 rounded-full mt-2 animate-bounce\"></div>\n        </div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,WAAW;IACf;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;gCAA0F,OAAO;oCAAE,gBAAgB;gCAAK;;;;;;;;;;;;;;;;;;0BAI3I,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;wCACX;wCACU;sDACT,6LAAC;4CAAK,WAAU;sDAA2E;;;;;;;;;;;;8CAK7F,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;;;;;;;sCAOH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,6LAAC;4CAAK,WAAU;sDAA0B;;;;;;;mCAJrC;;;;;;;;;;sCAUX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;8CAG5B,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAKpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;KA/HgB", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { \n  WrenchScrewdriverIcon, \n  BoltIcon, \n  PaintBrushIcon, \n  CubeIcon,\n  HomeIcon,\n  CogIcon\n} from '@heroicons/react/24/outline';\n\nconst services = [\n  {\n    icon: WrenchScrewdriverIcon,\n    title: 'Loodgieterswerk',\n    description: 'Lek<PERSON> repareren, kranen vervangen, sanitair installeren en alle andere loodgietersklussen.',\n    features: ['Lekkage reparatie', '<PERSON>raan installatie', 'Sanitair', 'Ontstopping']\n  },\n  {\n    icon: BoltIcon,\n    title: 'Elektra',\n    description: 'Veilige elektrische installaties, stopcontacten, schakelaars en verlichting.',\n    features: ['Stopcontacten', 'Verlichting', 'Schakelaars', 'Meterkast']\n  },\n  {\n    icon: PaintBrushIcon,\n    title: 'Schilderwerk',\n    description: 'Professioneel binnen- en buitenschilderwerk voor een frisse uitstraling.',\n    features: ['Binnenschilderen', '<PERSON><PERSON><PERSON>childeren', 'Behangen', 'Voorbehandeling']\n  },\n  {\n    icon: CubeIcon,\n    title: 'Mont<PERSON>',\n    description: '<PERSON><PERSON><PERSON>, keukens, kasten en andere montagewerk vakkundig uitgevoerd.',\n    features: ['Meubelmontage', 'Keukeninstallatie', 'Kastenwand', 'IKEA montage']\n  },\n  {\n    icon: HomeIcon,\n    title: 'Tegelwerk',\n    description: 'Badkamers, keukens en vloeren betegelen met oog voor detail.',\n    features: ['Badkamertegels', 'Keukentegels', 'Vloertegels', 'Voegen']\n  },\n  {\n    icon: CogIcon,\n    title: 'Timmerwerk',\n    description: 'Maatwerk timmerwerk, reparaties en aanpassingen in hout.',\n    features: ['Maatwerk', 'Reparaties', 'Deuren', 'Kozijnen']\n  }\n];\n\nexport function ServicesSection() {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n            Onze <span className=\"text-blue-600\">Diensten</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Van kleine reparaties tot grote projecten - wij hebben de expertise \n            voor al uw klussen in en om het huis.\n          </p>\n        </motion.div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <motion.div\n              key={service.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100\">\n                {/* Icon */}\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\">\n                  <service.icon className=\"w-8 h-8 text-white\" />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\n                  {service.title}\n                </h3>\n                \n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n\n                {/* Features */}\n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature) => (\n                    <li key={feature} className=\"flex items-center text-sm text-gray-500\">\n                      <div className=\"w-1.5 h-1.5 bg-blue-500 rounded-full mr-3\"></div>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n\n                {/* CTA */}\n                <Link\n                  href=\"/diensten\"\n                  className=\"inline-flex items-center text-blue-600 font-semibold hover:text-blue-700 transition-colors group-hover:translate-x-1 transform duration-200\"\n                >\n                  Meer informatie\n                  <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-lg text-gray-600 mb-8\">\n            Staat uw klus er niet bij? Geen probleem! Wij helpen u graag met alle soorten klussen.\n          </p>\n          <Link\n            href=\"/klus-plaatsen\"\n            className=\"inline-flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n          >\n            Plaats uw klus\n            <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAaA,MAAM,WAAW;IACf;QACE,MAAM,4OAAA,CAAA,wBAAqB;QAC3B,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAqB;YAAqB;YAAY;SAAc;IACjF;IACA;QACE,MAAM,kNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAiB;YAAe;YAAe;SAAY;IACxE;IACA;QACE,MAAM,8NAAA,CAAA,iBAAc;QACpB,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAoB;YAAoB;YAAY;SAAkB;IACnF;IACA;QACE,MAAM,kNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAiB;YAAqB;YAAc;SAAe;IAChF;IACA;QACE,MAAM,kNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAkB;YAAgB;YAAe;SAAS;IACvE;IACA;QACE,MAAM,gNAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAY;YAAc;YAAU;SAAW;IAC5D;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAoD;8CAC3D,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAI1B,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAGhB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,6LAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,6LAAC;gDAAiB,WAAU;;kEAC1B,6LAAC;wDAAI,WAAU;;;;;;oDACd;;+CAFM;;;;;;;;;;kDAQb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;2BAvCtE,QAAQ,KAAK;;;;;;;;;;8BAgDxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,6LAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;KAhGgB", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  ShieldCheckIcon, \n  ClockIcon, \n  CurrencyEuroIcon, \n  UserGroupIcon,\n  StarIcon,\n  PhoneIcon\n} from '@heroicons/react/24/outline';\n\nconst reasons = [\n  {\n    icon: ShieldCheckIcon,\n    title: 'Betrouwbaar & Verzekerd',\n    description: 'Volledig verzekerd en gecertificeerd. Uw klus is in veilige handen met onze ervaren vakmannen.',\n    color: 'from-green-500 to-emerald-600'\n  },\n  {\n    icon: ClockIcon,\n    title: 'Snelle Service',\n    description: 'Vaak nog dezelfde dag beschikbaar. Wij begrijpen dat sommige klussen niet kunnen wachten.',\n    color: 'from-blue-500 to-cyan-600'\n  },\n  {\n    icon: CurrencyEuroIcon,\n    title: 'Eerlijke Prijzen',\n    description: 'Transparante prijzen zonder verrassingen. U weet vooraf waar u aan toe bent.',\n    color: 'from-purple-500 to-indigo-600'\n  },\n  {\n    icon: UserGroupIcon,\n    title: '<PERSON><PERSON><PERSON> Team',\n    description: 'Ons team bestaat uit ervaren vakmannen met jarenlange expertise in hun vakgebied.',\n    color: 'from-orange-500 to-red-600'\n  },\n  {\n    icon: StarIcon,\n    title: 'Kwaliteitsgarantie',\n    description: 'Wij staan achter ons werk. Niet tevreden? Dan maken wij het kosteloos in orde.',\n    color: 'from-yellow-500 to-orange-600'\n  },\n  {\n    icon: PhoneIcon,\n    title: '24/7 Bereikbaar',\n    description: 'Voor spoedklussen zijn wij altijd bereikbaar. Dag en nacht, weekend en feestdagen.',\n    color: 'from-teal-500 to-green-600'\n  }\n];\n\nexport function WhyChooseUsSection() {\n  return (\n    <section className=\"py-20 bg-white relative overflow-hidden\">\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-50\"></div>\n      <div className=\"absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-l from-blue-100 to-transparent rounded-full blur-3xl opacity-30\"></div>\n      <div className=\"absolute bottom-1/4 left-0 w-96 h-96 bg-gradient-to-r from-indigo-100 to-transparent rounded-full blur-3xl opacity-30\"></div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n            Waarom <span className=\"text-blue-600\">ASklussen.nl</span>?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Ontdek waarom honderden klanten ons vertrouwen voor hun klussen. \n            Kwaliteit, betrouwbaarheid en service staan bij ons voorop.\n          </p>\n        </motion.div>\n\n        {/* Reasons Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {reasons.map((reason, index) => (\n            <motion.div\n              key={reason.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100/50\">\n                {/* Icon */}\n                <div className={`w-16 h-16 bg-gradient-to-br ${reason.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>\n                  <reason.icon className=\"w-8 h-8 text-white\" />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors\">\n                  {reason.title}\n                </h3>\n                \n                <p className=\"text-gray-600 leading-relaxed\">\n                  {reason.description}\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"mt-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-3xl p-8 md:p-12 text-white\"\n        >\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              Cijfers die voor zich spreken\n            </h3>\n            <p className=\"text-blue-100 text-lg\">\n              Onze resultaten in getallen\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n            <div className=\"space-y-2\">\n              <div className=\"text-4xl md:text-5xl font-bold\">500+</div>\n              <div className=\"text-blue-200\">Tevreden Klanten</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-4xl md:text-5xl font-bold\">98%</div>\n              <div className=\"text-blue-200\">Klanttevredenheid</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-4xl md:text-5xl font-bold\">5★</div>\n              <div className=\"text-blue-200\">Gemiddelde Beoordeling</div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"text-4xl md:text-5xl font-bold\">24u</div>\n              <div className=\"text-blue-200\">Reactietijd</div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAYA,MAAM,UAAU;IACd;QACE,MAAM,gOAAA,CAAA,kBAAe;QACrB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,kOAAA,CAAA,mBAAgB;QACtB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,4NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,kNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;;oCAAoD;kDACzD,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAmB;;;;;;;0CAE5D,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAOzD,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAW,CAAC,4BAA4B,EAAE,OAAO,KAAK,CAAC,mHAAmH,CAAC;sDAC9K,cAAA,6LAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAIzB,6LAAC;4CAAG,WAAU;sDACX,OAAO,KAAK;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDACV,OAAO,WAAW;;;;;;;;;;;;+BAnBlB,OAAO,KAAK;;;;;;;;;;kCA2BvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDAGpD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiC;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiC;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiC;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiC;;;;;;0DAChD,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;KA/FgB", "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\n\nconst testimonials = [\n  {\n    name: '<PERSON>',\n    location: 'Amsterdam',\n    rating: 5,\n    text: 'Fantastische service! De loodgieter kwam binnen 2 uur en heeft mijn lekkende kraan perfect gerepareerd. Zeer professioneel en vriendelijk.',\n    service: 'Loodgieterswerk',\n    date: '2 weken geleden'\n  },\n  {\n    name: '<PERSON>',\n    location: 'Utrecht',\n    rating: 5,\n    text: 'Mijn keuken is prachtig betegeld door het team van ASklussen. Vakwerk van hoge kwaliteit en netjes opgeruimd achtergelaten.',\n    service: 'Tegelwerk',\n    date: '1 maand geleden'\n  },\n  {\n    name: '<PERSON>',\n    location: 'Rotterdam',\n    rating: 5,\n    text: 'Snelle en betrouwbare service voor het ophangen van lampen en het installeren van extra stopcontacten. Aanrader!',\n    service: 'Elektra',\n    date: '3 weken geleden'\n  },\n  {\n    name: '<PERSON>',\n    location: '<PERSON>',\n    rating: 5,\n    text: '<PERSON><PERSON> woonkamer geschilderd. Perfecte afwerking en zeer nette werkwijze. Prijs-kwaliteit verhouding is uitstekend.',\n    service: 'Schilderwerk',\n    date: '2 maanden geleden'\n  },\n  {\n    name: 'Linda Bakker',\n    location: 'Eindhoven',\n    rating: 5,\n    text: 'IKEA keuken gemonteerd binnen één dag. Zeer ervaren monteurs die weten wat ze doen. Top service!',\n    service: 'Montage',\n    date: '1 maand geleden'\n  },\n  {\n    name: 'Tom van Dijk',\n    location: 'Groningen',\n    rating: 5,\n    text: 'Badkamer volledig gerenoveerd. Van tegels tot sanitair, alles perfect uitgevoerd. Zeer tevreden met het resultaat.',\n    service: 'Renovatie',\n    date: '6 weken geleden'\n  }\n];\n\nfunction StarRating({ rating }: { rating: number }) {\n  return (\n    <div className=\"flex space-x-1\">\n      {[1, 2, 3, 4, 5].map((star) => (\n        <div key={star}>\n          {star <= rating ? (\n            <StarIcon className=\"w-5 h-5 text-yellow-400\" />\n          ) : (\n            <StarOutlineIcon className=\"w-5 h-5 text-gray-300\" />\n          )}\n        </div>\n      ))}\n    </div>\n  );\n}\n\nexport function TestimonialsSection() {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\n            Wat onze <span className=\"text-blue-600\">klanten</span> zeggen\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Lees de ervaringen van onze tevreden klanten. Hun vertrouwen \n            en positieve feedback motiveren ons elke dag opnieuw.\n          </p>\n        </motion.div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <motion.div\n              key={`${testimonial.name}-${index}`}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 h-full flex flex-col\">\n                {/* Header */}\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-bold text-gray-900 text-lg\">{testimonial.name}</h4>\n                    <p className=\"text-gray-500 text-sm\">{testimonial.location}</p>\n                  </div>\n                  <StarRating rating={testimonial.rating} />\n                </div>\n\n                {/* Service badge */}\n                <div className=\"mb-4\">\n                  <span className=\"inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full\">\n                    {testimonial.service}\n                  </span>\n                </div>\n\n                {/* Testimonial text */}\n                <blockquote className=\"text-gray-600 leading-relaxed mb-6 flex-1\">\n                  \"{testimonial.text}\"\n                </blockquote>\n\n                {/* Date */}\n                <div className=\"text-gray-400 text-sm\">\n                  {testimonial.date}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom section with overall rating */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg max-w-2xl mx-auto\">\n            <div className=\"flex items-center justify-center space-x-4 mb-4\">\n              <div className=\"flex space-x-1\">\n                {[1, 2, 3, 4, 5].map((star) => (\n                  <StarIcon key={star} className=\"w-8 h-8 text-yellow-400\" />\n                ))}\n              </div>\n              <span className=\"text-3xl font-bold text-gray-900\">4.9</span>\n            </div>\n            <p className=\"text-gray-600 text-lg mb-4\">\n              Gemiddelde beoordeling op basis van <strong>247 reviews</strong>\n            </p>\n            <p className=\"text-gray-500\">\n              98% van onze klanten beveelt ons aan bij familie en vrienden\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,SAAS;QACT,MAAM;IACR;IACA;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,SAAS;QACT,MAAM;IACR;IACA;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,SAAS;QACT,MAAM;IACR;IACA;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,SAAS;QACT,MAAM;IACR;IACA;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,SAAS;QACT,MAAM;IACR;IACA;QACE,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,SAAS;QACT,MAAM;IACR;CACD;AAED,SAAS,WAAW,EAAE,MAAM,EAAsB;IAChD,qBACE,6LAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;0BACE,QAAQ,uBACP,6LAAC,gNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;yCAEpB,6LAAC,kNAAA,CAAA,WAAe;oBAAC,WAAU;;;;;;eAJrB;;;;;;;;;;AAUlB;KAdS;AAgBF,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;;gCAAoD;8CACvD,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;gCAAc;;;;;;;sCAEzD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC,YAAY,IAAI;;;;;;kEACjE,6LAAC;wDAAE,WAAU;kEAAyB,YAAY,QAAQ;;;;;;;;;;;;0DAE5D,6LAAC;gDAAW,QAAQ,YAAY,MAAM;;;;;;;;;;;;kDAIxC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,YAAY,OAAO;;;;;;;;;;;kDAKxB,6LAAC;wCAAW,WAAU;;4CAA4C;4CAC9D,YAAY,IAAI;4CAAC;;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;kDACZ,YAAY,IAAI;;;;;;;;;;;;2BA/BhB,GAAG,YAAY,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;8BAuCzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC,gNAAA,CAAA,WAAQ;gDAAY,WAAU;+CAAhB;;;;;;;;;;kDAGnB,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,6LAAC;gCAAE,WAAU;;oCAA6B;kDACJ,6LAAC;kDAAO;;;;;;;;;;;;0CAE9C,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;MA3FgB", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ArrowRightIcon, PhoneIcon, CalendarIcon } from '@heroicons/react/24/outline';\n\nexport function CTASection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 relative overflow-hidden\">\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 bg-black/20\"></div>\n      <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl\"></div>\n      <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-indigo-400/10 rounded-full blur-3xl\"></div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          {/* Main heading */}\n          <h2 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n            Klaar om te beginnen?\n          </h2>\n          \n          <p className=\"text-xl md:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed\">\n            Laat ons uw klus vakkundig uitvoeren. Vraag vandaag nog een \n            vrijblijvende offerte aan of plan direct een afspraak in.\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Link\n                href=\"/klus-plaatsen\"\n                className=\"group bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 shadow-2xl hover:shadow-blue-500/25 flex items-center space-x-3\"\n              >\n                <span>Plaats uw klus</span>\n                <ArrowRightIcon className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </Link>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Link\n                href=\"/afspraak-maken\"\n                className=\"group bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold text-lg border border-white/20 hover:bg-white/20 transition-all duration-300 flex items-center space-x-3\"\n              >\n                <CalendarIcon className=\"w-5 h-5\" />\n                <span>Plan afspraak</span>\n              </Link>\n            </motion.div>\n\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <a\n                href=\"tel:+31612345678\"\n                className=\"group bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center space-x-3\"\n              >\n                <PhoneIcon className=\"w-5 h-5\" />\n                <span>Bel direct</span>\n              </a>\n            </motion.div>\n          </div>\n\n          {/* Contact info cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\"\n            >\n              <PhoneIcon className=\"w-8 h-8 text-blue-300 mx-auto mb-4\" />\n              <h3 className=\"text-white font-semibold text-lg mb-2\">Bel ons</h3>\n              <p className=\"text-blue-100 mb-3\">Direct contact met onze experts</p>\n              <a href=\"tel:+31612345678\" className=\"text-blue-300 font-semibold hover:text-white transition-colors\">\n                06-12345678\n              </a>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n              className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\"\n            >\n              <CalendarIcon className=\"w-8 h-8 text-blue-300 mx-auto mb-4\" />\n              <h3 className=\"text-white font-semibold text-lg mb-2\">Plan online</h3>\n              <p className=\"text-blue-100 mb-3\">Kies zelf uw gewenste tijd</p>\n              <Link href=\"/afspraak-maken\" className=\"text-blue-300 font-semibold hover:text-white transition-colors\">\n                Afspraak maken\n              </Link>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20\"\n            >\n              <ArrowRightIcon className=\"w-8 h-8 text-blue-300 mx-auto mb-4\" />\n              <h3 className=\"text-white font-semibold text-lg mb-2\">Klus plaatsen</h3>\n              <p className=\"text-blue-100 mb-3\">Beschrijf uw klus online</p>\n              <Link href=\"/klus-plaatsen\" className=\"text-blue-300 font-semibold hover:text-white transition-colors\">\n                Start hier\n              </Link>\n            </motion.div>\n          </div>\n\n          {/* Trust indicators */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.5 }}\n            viewport={{ once: true }}\n            className=\"mt-16 flex flex-wrap justify-center items-center gap-8 text-blue-200\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n              <span>Verzekerd & Gecertificeerd</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n              <span>Geen voorrijkosten</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n              <span>Gratis offerte</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n              <span>100% Garantie</span>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,6LAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAI/D,6LAAC;4BAAE,WAAU;sCAA4E;;;;;;sCAMzF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC;0DAAK;;;;;;0DACN,6LAAC,8NAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAIV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAE,MAAK;4CAAmB,WAAU;sDAAiE;;;;;;;;;;;;8CAKxG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDAAiE;;;;;;;;;;;;8CAK1G,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,WAAU;sDAAiE;;;;;;;;;;;;;;;;;;sCAO3G,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;KAjJgB", "debugId": null}}]}