{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Bars3Icon, XMarkIcon, PhoneIcon } from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: '<PERSON><PERSON><PERSON>', href: '/diensten' },\n  { name: 'Tarieven', href: '/tarieven' },\n  { name: 'Projecten', href: '/projecten' },\n  { name: 'Beoordelingen', href: '/beoordelingen' },\n  { name: 'Over Ons', href: '/over-ons' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <nav\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/90 backdrop-blur-md shadow-lg border-b border-gray-200/50'\n          : 'bg-transparent'\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">AS</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">klussen.nl</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full\"></span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button & Phone */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <a\n              href=\"tel:+31612345678\"\n              className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              <PhoneIcon className=\"w-5 h-5\" />\n              <span className=\"font-medium\">06-12345678</span>\n            </a>\n            <Link\n              href=\"/klus-plaatsen\"\n              className=\"bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\n            >\n              Klus Plaatsen\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"lg:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 transition-colors\"\n          >\n            {isOpen ? (\n              <XMarkIcon className=\"w-6 h-6\" />\n            ) : (\n              <Bars3Icon className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"lg:hidden bg-white border-t border-gray-200 shadow-lg\"\n          >\n            <div className=\"px-4 py-4 space-y-3\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className=\"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-3 border-t border-gray-200 space-y-3\">\n                <a\n                  href=\"tel:+31612345678\"\n                  className=\"flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors\"\n                >\n                  <PhoneIcon className=\"w-5 h-5\" />\n                  <span className=\"font-medium\">06-12345678</span>\n                </a>\n                <Link\n                  href=\"/klus-plaatsen\"\n                  onClick={() => setIsOpen(false)}\n                  className=\"block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg font-medium text-center hover:from-blue-700 hover:to-blue-800 transition-all duration-200\"\n                >\n                  Klus Plaatsen\n                </Link>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAiB,MAAM;IAAiB;IAChD;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,uEACA;;0BAGN,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAEhC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBACC,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;qDAErB,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAO7B,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAET,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,SAAS,IAAM,UAAU;wCACzB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAxHgB;KAAA", "debugId": null}}]}