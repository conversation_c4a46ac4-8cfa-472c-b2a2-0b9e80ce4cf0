{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/src/app/projecten/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  MagnifyingGlassIcon,\n  FunnelIcon,\n  EyeIcon\n} from '@heroicons/react/24/outline';\n\nconst categories = [\n  'Alle projecten',\n  'Loodgieterswerk',\n  'Elektra',\n  'Schilderwerk',\n  'Montage',\n  'Tegelwerk',\n  'Timmerwerk',\n  'Renovatie'\n];\n\nconst projects = [\n  {\n    id: 1,\n    title: 'Badkamer renovatie Amsterdam',\n    category: 'Renovatie',\n    description: 'Complete badkamerrenovatie inclusief tegelwerk, sanitair en verlichting.',\n    duration: '5 dagen',\n    location: 'Amsterdam Noord',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Tegelwerk', 'Loodgieterswerk', 'Elektra']\n  },\n  {\n    id: 2,\n    title: 'Keuken installatie Utrecht',\n    category: 'Montage',\n    description: 'IKEA keuken montage met inbouwapparatuur en werkblad installatie.',\n    duration: '2 dagen',\n    location: 'Utrecht Centrum',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Mont<PERSON>', 'Elektra']\n  },\n  {\n    id: 3,\n    title: 'Woonkamer schilderwerk',\n    category: 'Schilderwerk',\n    description: 'Complete woonkamer geschilderd inclusief plafond en houtwerk.',\n    duration: '3 dagen',\n    location: 'Amsterdam West',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Schilderwerk']\n  },\n  {\n    id: 4,\n    title: 'CV-ketel vervanging',\n    category: 'Loodgieterswerk',\n    description: 'Oude CV-ketel vervangen door moderne HR-ketel inclusief leidingwerk.',\n    duration: '1 dag',\n    location: 'Amstelveen',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Loodgieterswerk']\n  },\n  {\n    id: 5,\n    title: 'Meterkast uitbreiding',\n    category: 'Elektra',\n    description: 'Meterkast uitgebreid met extra groepen en aardlekschakelaars.',\n    duration: '1 dag',\n    location: 'Haarlem',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Elektra']\n  },\n  {\n    id: 6,\n    title: 'Toilet renovatie',\n    category: 'Tegelwerk',\n    description: 'Toilet volledig opnieuw betegeld met moderne wandtegels.',\n    duration: '2 dagen',\n    location: 'Amsterdam Zuid',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Tegelwerk', 'Loodgieterswerk']\n  },\n  {\n    id: 7,\n    title: 'Maatwerk boekenkast',\n    category: 'Timmerwerk',\n    description: 'Op maat gemaakte boekenkast van vloer tot plafond.',\n    duration: '4 dagen',\n    location: 'Zaandam',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Timmerwerk']\n  },\n  {\n    id: 8,\n    title: 'Kantoor verlichting',\n    category: 'Elektra',\n    description: 'LED verlichting geïnstalleerd in kantoorruimte met dimfunctie.',\n    duration: '1 dag',\n    location: 'Amsterdam Oost',\n    beforeImage: '/api/placeholder/400/300',\n    afterImage: '/api/placeholder/400/300',\n    tags: ['Elektra']\n  }\n];\n\nexport default function ProjectenPage() {\n  const [selectedCategory, setSelectedCategory] = useState('Alle projecten');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedProject, setSelectedProject] = useState<typeof projects[0] | null>(null);\n\n  const filteredProjects = projects.filter(project => {\n    const matchesCategory = selectedCategory === 'Alle projecten' || project.category === selectedCategory;\n    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    return matchesCategory && matchesSearch;\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 pt-20\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-900 to-blue-800 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Onze <span className=\"text-blue-300\">Projecten</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto\">\n              Bekijk een selectie van onze afgeronde projecten en laat u inspireren\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Filters */}\n      <section className=\"py-8 bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex flex-col lg:flex-row gap-6 items-center justify-between\">\n            {/* Search */}\n            <div className=\"relative flex-1 max-w-md\">\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"Zoek projecten...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            {/* Category Filter */}\n            <div className=\"flex flex-wrap gap-2\">\n              {categories.map((category) => (\n                <button\n                  key={category}\n                  onClick={() => setSelectedCategory(category)}\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${\n                    selectedCategory === category\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }`}\n                >\n                  {category}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Grid */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-12\"\n          >\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              {filteredProjects.length} project{filteredProjects.length !== 1 ? 'en' : ''} gevonden\n            </h2>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {filteredProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden group\"\n              >\n                {/* Project Image */}\n                <div className=\"relative h-48 bg-gray-200 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10\"></div>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"text-gray-400 text-center\">\n                      <div className=\"w-16 h-16 bg-gray-300 rounded-lg mx-auto mb-2\"></div>\n                      <p className=\"text-sm\">Voor/na foto's</p>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setSelectedProject(project)}\n                    className=\"absolute top-4 right-4 z-20 bg-white/90 backdrop-blur-sm p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity\"\n                  >\n                    <EyeIcon className=\"w-5 h-5 text-gray-700\" />\n                  </button>\n                </div>\n\n                {/* Project Info */}\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <h3 className=\"text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors\">\n                      {project.title}\n                    </h3>\n                    <span className=\"bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full\">\n                      {project.category}\n                    </span>\n                  </div>\n\n                  <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                    {project.description}\n                  </p>\n\n                  <div className=\"space-y-2 mb-4\">\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-gray-500\">Duur:</span>\n                      <span className=\"font-medium text-gray-900\">{project.duration}</span>\n                    </div>\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-gray-500\">Locatie:</span>\n                      <span className=\"font-medium text-gray-900\">{project.location}</span>\n                    </div>\n                  </div>\n\n                  {/* Tags */}\n                  <div className=\"flex flex-wrap gap-2\">\n                    {project.tags.map((tag) => (\n                      <span\n                        key={tag}\n                        className=\"bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full\"\n                      >\n                        {tag}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {filteredProjects.length === 0 && (\n            <div className=\"text-center py-12\">\n              <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <MagnifyingGlassIcon className=\"w-8 h-8 text-gray-400\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                Geen projecten gevonden\n              </h3>\n              <p className=\"text-gray-600\">\n                Probeer een andere zoekterm of categorie\n              </p>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {/* Project Modal */}\n      {selectedProject && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-start mb-6\">\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                    {selectedProject.title}\n                  </h2>\n                  <span className=\"bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full\">\n                    {selectedProject.category}\n                  </span>\n                </div>\n                <button\n                  onClick={() => setSelectedProject(null)}\n                  className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n                >\n                  ×\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-2\">Voor</h3>\n                  <div className=\"h-48 bg-gray-200 rounded-lg flex items-center justify-center\">\n                    <span className=\"text-gray-400\">Voor foto</span>\n                  </div>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-gray-900 mb-2\">Na</h3>\n                  <div className=\"h-48 bg-gray-200 rounded-lg flex items-center justify-center\">\n                    <span className=\"text-gray-400\">Na foto</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <p className=\"text-gray-600 leading-relaxed\">\n                  {selectedProject.description}\n                </p>\n                \n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <span className=\"text-gray-500\">Duur:</span>\n                    <span className=\"ml-2 font-medium\">{selectedProject.duration}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Locatie:</span>\n                    <span className=\"ml-2 font-medium\">{selectedProject.location}</span>\n                  </div>\n                </div>\n\n                <div>\n                  <span className=\"text-gray-500\">Diensten:</span>\n                  <div className=\"flex flex-wrap gap-2 mt-2\">\n                    {selectedProject.tags.map((tag) => (\n                      <span\n                        key={tag}\n                        className=\"bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full\"\n                      >\n                        {tag}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      )}\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-blue-600 to-blue-700\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n              Wilt u ook zo'n mooi resultaat?\n            </h2>\n            <p className=\"text-xl text-blue-100 mb-8\">\n              Laat ons uw project realiseren met dezelfde zorg en vakmanschap\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"/klus-plaatsen\"\n                className=\"bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-colors\"\n              >\n                Start uw project\n              </a>\n              <a\n                href=\"/contact\"\n                className=\"bg-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-900 transition-colors\"\n              >\n                Vraag advies\n              </a>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAUA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;YAAa;YAAmB;SAAU;IACnD;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;YAAW;SAAU;IAC9B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;SAAe;IACxB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;SAAkB;IAC3B;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;SAAU;IACnB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;YAAa;SAAkB;IACxC;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;SAAa;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,aAAa;QACb,YAAY;QACZ,MAAM;YAAC;SAAU;IACnB;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAElF,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,kBAAkB,qBAAqB,oBAAoB,QAAQ,QAAQ,KAAK;QACtF,MAAM,gBAAgB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC/F,OAAO,mBAAmB;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAG,WAAU;;oCAAsC;kDAC7C,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAEvC,6LAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;;;;;0BAQzE,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,6DAA6D,EACvE,qBAAqB,WACjB,2BACA,+CACJ;kDAED;uCARI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiBjB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAG,WAAU;;oCACX,iBAAiB,MAAM;oCAAC;oCAAS,iBAAiB,MAAM,KAAK,IAAI,OAAO;oCAAG;;;;;;;;;;;;sCAIhF,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAE,WAAU;0EAAU;;;;;;;;;;;;;;;;;8DAG3B,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DAEV,cAAA,6LAAC,gNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,6LAAC;4DAAK,WAAU;sEACb,QAAQ,QAAQ;;;;;;;;;;;;8DAIrB,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAA6B,QAAQ,QAAQ;;;;;;;;;;;;sEAE/D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEAAK,WAAU;8EAA6B,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;8DAKjE,6LAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,6LAAC;4DAEC,WAAU;sEAET;2DAHI;;;;;;;;;;;;;;;;;mCAtDR,QAAQ,EAAE;;;;;;;;;;wBAkEpB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;YASpC,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,gBAAgB,KAAK;;;;;;0DAExB,6LAAC;gDAAK,WAAU;0DACb,gBAAgB,QAAQ;;;;;;;;;;;;kDAG7B,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAGpC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAKtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,gBAAgB,WAAW;;;;;;kDAG9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAoB,gBAAgB,QAAQ;;;;;;;;;;;;0DAE9D,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAoB,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;kDAIhE,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAI,WAAU;0DACZ,gBAAgB,IAAI,CAAC,GAAG,CAAC,CAAC,oBACzB,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAevB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,6LAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAvRwB;KAAA", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/node_modules/%40heroicons/react/24/outline/esm/MagnifyingGlassIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,oBAAoB,EAC3B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/website%202/asklussen-website/node_modules/%40heroicons/react/24/outline/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}